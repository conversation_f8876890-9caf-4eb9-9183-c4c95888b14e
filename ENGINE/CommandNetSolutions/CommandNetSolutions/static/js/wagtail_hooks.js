document.addEventListener("DOMContentLoaded", function() {
    // Target the Airflow link
    const airflowMenuItem = document.querySelector('a[href="http://localhost:8080"]');
    if (airflowMenuItem) {
        airflowMenuItem.setAttribute("target", "_blank");
    }

    // Target the CommandNet Solutions link
    const commandNetMenuItem = document.querySelector('a[href="https://www.commandnetsolutions.com"]');
    if (commandNetMenuItem) {
        commandNetMenuItem.setAttribute("target", "_blank");
    }

    // Target the ChatGPT link
    const chatGptMenuItem = document.querySelector('a[href="https://chatgpt.com/g/g-Nw4zf8xvh-commandnet"]');
    if (chatGptMenuItem) {
        chatGptMenuItem.setAttribute("target", "_blank");
    }
});
