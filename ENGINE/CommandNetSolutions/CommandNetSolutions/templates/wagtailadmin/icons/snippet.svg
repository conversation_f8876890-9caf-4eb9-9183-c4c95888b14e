<svg  id="icon-snippet" class="icon--directional"  width='20' height='20' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='24' height='24' stroke='none' fill='#000000' opacity='0'/>


<g transform="matrix(0.83 0 0 0.83 12 12)" >
<g style="" >
<g transform="matrix(1 0 0 1 0 -7.5)" >
<path style="stroke: rgb(0,0,0); stroke-width: 1.5; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -4.5)" d="M 8.25 4.5 C 8.25 6.5710678118654755 9.928932188134524 8.25 12 8.25 C 14.071067811865476 8.25 15.75 6.5710678118654755 15.75 4.5 C 15.75 2.4289321881345245 14.071067811865476 0.75 12 0.75 C 9.928932188134524 0.75 8.25 2.428932188134524 8.25 4.499999999999999 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0 0.17)" >
<path style="stroke: rgb(0,0,0); stroke-width: 1.5; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -12.17)" d="M 9.29 7.092 C 10.875 10.478 9.19 13.864 7.499999999999999 17.25 L 16.5 17.25 C 14.807 13.864 13.125 10.478 14.71 7.0920000000000005" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0 9.75)" >
<path style="stroke: rgb(0,0,0); stroke-width: 1.5; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -21.75)" d="M 20.25 21.75 C 20.25 20.92157287525381 19.57842712474619 20.25 18.75 20.25 L 5.25 20.25 C 4.42157287525381 20.25 3.75 20.92157287525381 3.75 21.75 C 3.75 22.57842712474619 4.42157287525381 23.25 5.25 23.25 L 18.75 23.25 C 19.57842712474619 23.25 20.25 22.57842712474619 20.25 21.75 Z" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 0 6.75)" >
<path style="stroke: rgb(0,0,0); stroke-width: 1.5; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-12, -18.75)" d="M 16.5 17.25 L 7.5 17.25 C 6.124012398388193 17.57554287316503 5.177244629787122 18.83789989796646 5.25 20.25 L 18.75 20.25 C 18.822755370212878 18.83789989796646 17.875987601611808 17.57554287316503 16.5 17.25 Z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>