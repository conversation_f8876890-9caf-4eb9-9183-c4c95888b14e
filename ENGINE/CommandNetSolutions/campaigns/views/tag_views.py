"""
Views for managing tags in the campaigns app.
"""
import logging
import uuid
from django.contrib import messages
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
# Removed LoginRequiredMixin to disable authentication requirements
# from django.contrib.auth.mixins import LoginRequiredMixin

from campaigns.models import Campaign, CampaignTag
from campaigns.forms import CampaignTagForm

logger = logging.getLogger(__name__)


class CampaignTagListView(ListView):
    """
    View to list all tags assigned to a campaign.
    """
    model = CampaignTag
    template_name = 'campaigns/campaign_tags.html'
    context_object_name = 'campaign_tags'

    def get_queryset(self):
        self.campaign = get_object_or_404(Campaign, pk=self.kwargs['campaign_id'])
        return CampaignTag.objects.filter(campaign=self.campaign).select_related('tag')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['campaign'] = self.campaign
        context['form'] = CampaignTagForm(campaign=self.campaign)
        return context


class CampaignTagCreateView(CreateView):
    """
    View to assign a tag to a campaign.
    """
    model = CampaignTag
    form_class = CampaignTagForm
    template_name = 'campaigns/campaign_tag_form.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        self.campaign = get_object_or_404(Campaign, pk=self.kwargs['campaign_id'])
        kwargs['campaign'] = self.campaign
        return kwargs

    def form_valid(self, form):
        form.instance.campaign = self.campaign
        form.instance.id = uuid.uuid4()
        tag_name = form.instance.tag.name
        messages.success(self.request, f"Tag '{tag_name}' assigned to campaign successfully.")
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('campaigns:campaign_tags', kwargs={'campaign_id': self.campaign.id})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['campaign'] = self.campaign
        return context


class CampaignTagUpdateView(UpdateView):
    """
    View to update a campaign tag.
    """
    model = CampaignTag
    form_class = CampaignTagForm
    template_name = 'campaigns/campaign_tag_form.html'

    def get_success_url(self):
        return reverse('campaigns:campaign_tags', kwargs={'campaign_id': self.object.campaign.id})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['campaign'] = self.object.campaign
        return context


class CampaignTagDeleteView(DeleteView):
    """
    View to remove a tag from a campaign.
    """
    model = CampaignTag
    template_name = 'campaigns/campaign_tag_confirm_delete.html'

    def get_success_url(self):
        return reverse('campaigns:campaign_tags', kwargs={'campaign_id': self.object.campaign.id})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['campaign'] = self.object.campaign
        return context
