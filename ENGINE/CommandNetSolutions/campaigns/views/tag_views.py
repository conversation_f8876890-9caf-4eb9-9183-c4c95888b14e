"""
Views for managing tags in the campaigns app.
"""
import logging
import uuid
from django.contrib import messages
from django.shortcuts import get_object_or_404, render
from django.urls import reverse
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.http import JsonResponse
from django.db.models import Q
# Removed LoginRequiredMixin to disable authentication requirements
# from django.contrib.auth.mixins import LoginRequiredMixin

from campaigns.models import Campaign, CampaignTag, DynamicTag, TagCategory, TagGroup
from campaigns.forms import CampaignTagForm

logger = logging.getLogger(__name__)


class CampaignTagListView(ListView):
    """
    View to list all tags assigned to a campaign.
    """
    model = CampaignTag
    template_name = 'campaigns/campaign_tags.html'
    context_object_name = 'campaign_tags'

    def get_queryset(self):
        self.campaign = get_object_or_404(Campaign, pk=self.kwargs['campaign_id'])
        return CampaignTag.objects.filter(campaign=self.campaign).select_related('tag')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['campaign'] = self.campaign
        context['form'] = CampaignTagForm(campaign=self.campaign)
        return context


class CampaignTagCreateView(CreateView):
    """
    View to assign a tag to a campaign.
    """
    model = CampaignTag
    form_class = CampaignTagForm
    template_name = 'campaigns/campaign_tag_form.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        self.campaign = get_object_or_404(Campaign, pk=self.kwargs['campaign_id'])
        kwargs['campaign'] = self.campaign
        return kwargs

    def form_valid(self, form):
        form.instance.campaign = self.campaign
        form.instance.id = uuid.uuid4()
        tag_name = form.instance.tag.name
        messages.success(self.request, f"Tag '{tag_name}' assigned to campaign successfully.")
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('campaigns:campaign_tags', kwargs={'campaign_id': self.campaign.id})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['campaign'] = self.campaign
        return context


class CampaignTagUpdateView(UpdateView):
    """
    View to update a campaign tag.
    """
    model = CampaignTag
    form_class = CampaignTagForm
    template_name = 'campaigns/campaign_tag_form.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass the campaign to the form for proper initialization
        kwargs['campaign'] = self.object.campaign
        return kwargs

    def get_success_url(self):
        return reverse('campaigns:campaign_tags', kwargs={'campaign_id': self.object.campaign.id})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['campaign'] = self.object.campaign
        return context

    def form_valid(self, form):
        messages.success(self.request, f"Tag '{form.instance.tag.name}' updated successfully.")
        return super().form_valid(form)


class CampaignTagDeleteView(DeleteView):
    """
    View to remove a tag from a campaign.
    """
    model = CampaignTag
    template_name = 'campaigns/campaign_tag_confirm_delete.html'

    def get_success_url(self):
        return reverse('campaigns:campaign_tags', kwargs={'campaign_id': self.object.campaign.id})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['campaign'] = self.object.campaign
        return context


class TagSearchView(View):
    """
    HTMX view for searching tags with filtering capabilities.
    """
    def get(self, request, campaign_id):
        campaign = get_object_or_404(Campaign, pk=campaign_id)
        search_query = request.GET.get('search', '').strip()
        category_filter = request.GET.get('category', '').strip()

        # Get already assigned tag IDs for this campaign
        assigned_tag_ids = CampaignTag.objects.filter(campaign=campaign).values_list('tag_id', flat=True)

        # Start with global tags
        tags = DynamicTag.objects.filter(
            Q(is_global=True) | Q(tag_group__is_global=True)
        ).exclude(id__in=assigned_tag_ids)

        # Apply search filter
        if search_query:
            tags = tags.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query)
            )

        # Apply category filter
        if category_filter:
            tags = tags.filter(category_id=category_filter)

        tags = tags.select_related('category').order_by('name')[:50]  # Limit results

        return render(request, 'campaigns/htmx/tag_search_results.html', {
            'tags': tags,
            'campaign': campaign
        })


class TagGroupAssignView(View):
    """
    HTMX view for assigning all tags from a tag group to a campaign.
    """
    def post(self, request, campaign_id):
        campaign = get_object_or_404(Campaign, pk=campaign_id)
        tag_group_id = request.POST.get('tag_group_id')
        is_required = request.POST.get('is_required') == 'on'

        if not tag_group_id:
            return JsonResponse({'error': 'Tag group ID is required'}, status=400)

        try:
            tag_group = get_object_or_404(TagGroup, pk=tag_group_id)

            # Get already assigned tag IDs for this campaign
            assigned_tag_ids = set(CampaignTag.objects.filter(campaign=campaign).values_list('tag_id', flat=True))

            # Get tags from the group that aren't already assigned
            group_tags = tag_group.tags.exclude(id__in=assigned_tag_ids)

            created_count = 0
            for tag in group_tags:
                CampaignTag.objects.create(
                    id=uuid.uuid4(),
                    campaign=campaign,
                    tag=tag,
                    is_required=is_required
                )
                created_count += 1

            if created_count > 0:
                messages.success(
                    request,
                    f"Successfully assigned {created_count} tags from '{tag_group.name}' to the campaign."
                )
            else:
                messages.info(
                    request,
                    f"All tags from '{tag_group.name}' are already assigned to this campaign."
                )

            # Return the updated assigned tags list
            campaign_tags = CampaignTag.objects.filter(campaign=campaign).select_related('tag', 'tag__category')
            return render(request, 'campaigns/htmx/assigned_tags_list.html', {
                'campaign_tags': campaign_tags,
                'campaign': campaign
            })

        except Exception as e:
            logger.error(f"Error assigning tag group: {str(e)}")
            return JsonResponse({'error': 'Failed to assign tag group'}, status=500)


class TagCategoriesView(View):
    """
    HTMX view for getting tag categories for filtering.
    """
    def get(self, request):
        categories = TagCategory.objects.all().order_by('name')
        return JsonResponse({
            'categories': [
                {'id': str(cat.id), 'name': cat.name, 'color': cat.color}
                for cat in categories
            ]
        })


class TagGroupsView(View):
    """
    HTMX view for getting available tag groups.
    """
    def get(self, request, campaign_id):
        campaign = get_object_or_404(Campaign, pk=campaign_id)
        tag_groups = TagGroup.objects.filter(is_global=True).order_by('name')

        return render(request, 'campaigns/htmx/tag_groups_list.html', {
            'tag_groups': tag_groups,
            'campaign': campaign
        })
