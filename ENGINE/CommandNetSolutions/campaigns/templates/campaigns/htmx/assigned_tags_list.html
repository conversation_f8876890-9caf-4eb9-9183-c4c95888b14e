{% if campaign_tags %}
<!-- Bulk Actions Bar -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div class="d-flex align-items-center">
        <input type="checkbox" id="select-all-tags" class="form-check-input me-2">
        <label for="select-all-tags" class="form-check-label me-3">Select All</label>
        <span id="selected-count" class="text-muted">0 selected</span>
    </div>
    <div id="bulk-actions" class="btn-group" style="display: none;">
        <button type="button"
                class="btn btn-sm btn-success"
                onclick="performBulkAction('mark_required')"
                title="Mark selected tags as required"
                style="width: 40px; height: 32px;">
            <i class="fas fa-check"></i>
        </button>
        <button type="button"
                class="btn btn-sm btn-warning"
                onclick="performBulkAction('mark_optional')"
                title="Mark selected tags as optional"
                style="width: 40px; height: 32px;">
            <i class="fas fa-minus"></i>
        </button>
        <button type="button"
                class="btn btn-sm btn-danger"
                onclick="performBulkAction('delete')"
                title="Delete selected tags"
                style="width: 40px; height: 32px;">
            <i class="fas fa-trash"></i>
        </button>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-hover">
        <thead>
            <tr>
                <th width="40"></th>
                <th>Tag Name</th>
                <th>Category</th>
                <th>Required</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for campaign_tag in campaign_tags %}
            <tr>
                <td>
                    <input type="checkbox"
                           class="form-check-input tag-checkbox"
                           value="{{ campaign_tag.id }}"
                           onchange="updateBulkActions()">
                </td>
                <td>
                    <span class="badge bg-primary">{{ campaign_tag.tag.name }}</span>
                </td>
                <td>
                    {% if campaign_tag.tag.category %}
                    <span class="badge" style="background-color: {{ campaign_tag.tag.category.color }}">
                        {{ campaign_tag.tag.category.name }}
                    </span>
                    {% else %}
                    <span class="badge bg-secondary">No Category</span>
                    {% endif %}
                </td>
                <td>
                    <!-- Inline toggle for required status -->
                    <button type="button"
                            class="btn btn-sm {% if campaign_tag.is_required %}btn-success{% else %}btn-secondary{% endif %}"
                            hx-post="{% url 'campaigns:htmx_toggle_tag_required' campaign_tag.id %}"
                            hx-target="#assigned-tags-container"
                            hx-swap="innerHTML"
                            hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'>
                        {% if campaign_tag.is_required %}
                        <i class="fas fa-check me-1"></i>Required
                        {% else %}
                        <i class="fas fa-minus me-1"></i>Optional
                        {% endif %}
                    </button>
                </td>
                <td>
                    <button type="button"
                            class="btn btn-sm btn-outline-danger"
                            onclick="deleteTag('{{ campaign_tag.id }}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Helper function to get CSRF token
function getCsrfToken() {
    // Try multiple ways to get CSRF token
    let token = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (!token) {
        token = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
    }
    if (!token) {
        token = '{{ csrf_token }}';
    }
    return token;
}

// Bulk actions functionality
function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.tag-checkbox');
    const selectedCheckboxes = document.querySelectorAll('.tag-checkbox:checked');
    const selectAllCheckbox = document.getElementById('select-all-tags');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');

    // Update selected count
    selectedCount.textContent = `${selectedCheckboxes.length} selected`;

    // Show/hide bulk actions
    if (selectedCheckboxes.length > 0) {
        bulkActions.style.display = 'block';
    } else {
        bulkActions.style.display = 'none';
    }

    // Update select all checkbox state
    if (selectedCheckboxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (selectedCheckboxes.length === checkboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

// Select all functionality
document.getElementById('select-all-tags').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.tag-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkActions();
});

// Add event listeners to individual checkboxes
document.querySelectorAll('.tag-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActions);
});

// Perform bulk action
function performBulkAction(action) {
    const selectedCheckboxes = document.querySelectorAll('.tag-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (selectedIds.length === 0) {
        showAlert('Please select at least one tag.', 'warning');
        return;
    }

    let confirmMessage = '';
    switch(action) {
        case 'delete':
            confirmMessage = `Are you sure you want to delete ${selectedIds.length} tag(s)?`;
            break;
        case 'mark_required':
            confirmMessage = `Mark ${selectedIds.length} tag(s) as required?`;
            break;
        case 'mark_optional':
            confirmMessage = `Mark ${selectedIds.length} tag(s) as optional?`;
            break;
    }

    if (!confirm(confirmMessage)) {
        return;
    }

    // Show loading state
    showBulkActionLoading();

    // Create form data
    const formData = new FormData();
    formData.append('action', action);
    selectedIds.forEach(id => formData.append('selected_tags', id));

    // Send HTMX request with proper CSRF handling
    fetch('{% url "campaigns:htmx_bulk_tag_action" campaign.id %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        }
    }).then(response => {
        if (response.ok) {
            return response.text();
        }
        throw new Error('Network response was not ok');
    }).then(html => {
        document.getElementById('assigned-tags-container').innerHTML = html;
        hideBulkActionLoading();
        showAlert('Bulk action completed successfully!', 'success');
    }).catch((error) => {
        hideBulkActionLoading();
        showAlert('Failed to perform bulk action. Please try again.', 'danger');
        console.error('Bulk action error:', error);
    });
}

// Delete single tag
function deleteTag(tagId) {
    if (!confirm('Are you sure you want to delete this tag?')) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'delete');
    formData.append('selected_tags', tagId);

    fetch('{% url "campaigns:htmx_bulk_tag_action" campaign.id %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
        }
    }).then(response => {
        if (response.ok) {
            return response.text();
        }
        throw new Error('Network response was not ok');
    }).then(html => {
        document.getElementById('assigned-tags-container').innerHTML = html;
        showAlert('Tag deleted successfully!', 'success');
    }).catch((error) => {
        showAlert('Failed to delete tag. Please try again.', 'danger');
        console.error('Delete tag error:', error);
    });
}

// Helper functions for better UX
function showBulkActionLoading() {
    const bulkActions = document.getElementById('bulk-actions');
    const buttons = bulkActions.querySelectorAll('button');
    buttons.forEach(btn => {
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    });
}

function hideBulkActionLoading() {
    const bulkActions = document.getElementById('bulk-actions');
    const buttons = bulkActions.querySelectorAll('button');
    buttons.forEach((btn, index) => {
        btn.disabled = false;
        const originalIcons = [
            '<i class="fas fa-check"></i>',
            '<i class="fas fa-minus"></i>',
            '<i class="fas fa-trash"></i>'
        ];
        btn.innerHTML = originalIcons[index];
    });
}

function showAlert(message, type = 'info') {
    // Limit to maximum 3 notifications
    const container = document.getElementById('assigned-tags-container');

    // Remove oldest alerts if we already have 3 or more
    const existingAlerts = container.querySelectorAll('.alert');
    if (existingAlerts.length >= 3) {
        // Remove the oldest alerts (from the bottom)
        for (let i = existingAlerts.length - 1; i >= 2; i--) {
            existingAlerts[i].remove();
        }
    }

    // Create alert element with better color scheme
    const alertDiv = document.createElement('div');
    let alertClass = '';
    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            break;
        case 'danger':
            alertClass = 'alert-danger';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            break;
        default:
            alertClass = 'alert-info';
    }

    alertDiv.className = `alert ${alertClass} alert-dismissible fade show mb-3`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at top of assigned tags container
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Initialize bulk actions on load
updateBulkActions();
</script>

{% else %}
<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i> No tags have been assigned to this campaign yet.
</div>
{% endif %}
