{% if campaign_tags %}
<div class="table-responsive">
    <table class="table table-hover">
        <thead>
            <tr>
                <th>Tag Name</th>
                <th>Category</th>
                <th>Required</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for campaign_tag in campaign_tags %}
            <tr>
                <td>
                    <span class="badge bg-primary">{{ campaign_tag.tag.name }}</span>
                </td>
                <td>
                    {% if campaign_tag.tag.category %}
                    <span class="badge" style="background-color: {{ campaign_tag.tag.category.color }}">
                        {{ campaign_tag.tag.category.name }}
                    </span>
                    {% else %}
                    <span class="badge bg-secondary">No Category</span>
                    {% endif %}
                </td>
                <td>
                    {% if campaign_tag.is_required %}
                    <span class="badge bg-success">Required</span>
                    {% else %}
                    <span class="badge bg-secondary">Optional</span>
                    {% endif %}
                </td>
                <td>
                    <div class="btn-group">
                        <a href="{% url 'campaigns:update_campaign_tag' campaign_tag.id %}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="{% url 'campaigns:delete_campaign_tag' campaign_tag.id %}" class="btn btn-sm btn-outline-danger">
                            <i class="fas fa-trash"></i>
                        </a>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i> No tags have been assigned to this campaign yet.
</div>
{% endif %}
