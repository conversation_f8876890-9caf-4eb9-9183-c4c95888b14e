{% extends "campaigns/base.html" %}

{% block title %}Campaign Tags - {{ campaign.name }}{% endblock %}

{% block campaign_content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">Campaign Tags</h1>
            <p class="page-subtitle">Manage tags for campaign: {{ campaign.name }}</p>
        </div>
        <div>
            <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-tags me-2"></i>Assigned Tags</h5>
                </div>
                <div class="card-body">
                    {% if campaign_tags %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Tag Name</th>
                                    <th>Category</th>
                                    <th>Required</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for campaign_tag in campaign_tags %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">{{ campaign_tag.tag.name }}</span>
                                    </td>
                                    <td>
                                        {% if campaign_tag.tag.category %}
                                        <span class="badge" style="background-color: {{ campaign_tag.tag.category.color }}">
                                            {{ campaign_tag.tag.category.name }}
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">No Category</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if campaign_tag.is_required %}
                                        <span class="badge bg-success">Required</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Optional</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'campaigns:update_campaign_tag' campaign_tag.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'campaigns:delete_campaign_tag' campaign_tag.id %}" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No tags have been assigned to this campaign yet.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Assign New Tag</h5>
                </div>
                <div class="card-body">
                    <form action="{% url 'campaigns:create_campaign_tag' campaign.id %}" method="post">
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="{{ form.tag.id_for_label }}" class="form-label">Tag</label>
                            {{ form.tag.errors }}
                            {{ form.tag }}
                            <div class="form-text">Select a tag to assign to this campaign.</div>
                        </div>

                        <div class="mb-3 form-check">
                            {{ form.is_required }}
                            <label for="{{ form.is_required.id_for_label }}" class="form-check-label">Required for Whitelist</label>
                            <div class="form-text">If checked, accounts must match this tag to be included in the whitelist.</div>
                        </div>


                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-1"></i> Assign Tag
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>About Tags</h5>
                </div>
                <div class="card-body">
                    <p>Tags help you categorize accounts based on specific criteria. When you analyze accounts, they will be tagged based on these criteria.</p>
                    <p>Accounts that match required tags will be added to the campaign's whitelist.</p>
                    <p>You can create new tags from the <a href="{% url 'campaigns:dynamic_tag_list' %}">Tag Management</a> page.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
